"use client";

import AnimatedCounter from "@/components/AnimatedCounter";
import {
  <PERSON><PERSON>,
  FormCard,
  FormGrid,
} from "@/lib/forms";
import { formatAmount } from "@/lib/utils";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useSelectedStore } from "../../hooks/useStores";

interface DashboardStats {
  sales: {
    today: { total: number; count: number; growth: number };
    week: { total: number; count: number; growth: number };
    month: { total: number; count: number; growth: number };
    year: { total: number; count: number; growth: number };
  };
  inventory: {
    totalProducts: number;
    lowStockCount: number;
    lowStockPercentage: number;
  };
  customers: {
    total: number;
  };
}

interface RecentSale {
  id: number;
  total: number;
  createdAt: string;
  customer?: { name: string } | null;
  user: { name: string };
  itemCount: number;
}

interface TopProduct {
  id: number;
  name: string;
  category: string;
  revenue: number;
  units: number;
  growth: number;
}

interface LowStockProduct {
  id: number;
  name: string;
  currentStock: number;
  minStock: number;
  category: string;
}

export default function DashboardPage() {
  const { selectedStoreId, selectedStore, setSelectedStoreId, stores, isLoading: storesLoading } = useSelectedStore();
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [recentSales, setRecentSales] = useState<RecentSale[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [lowStockProducts, setLowStockProducts] = useState<LowStockProduct[]>([]);

  useEffect(() => {
    if (selectedStoreId) {
      fetchDashboardData();
    }
  }, [selectedStoreId]);

  const fetchDashboardData = async () => {
    if (!selectedStoreId) return;

    try {
      setIsLoading(true);

      // Add store filter to API calls
      const storeParam = `storeId=${selectedStoreId}`;

      // Fetch all dashboard data in parallel
      const [dashboardResponse, topProductsResponse, inventoryResponse, salesResponse] = await Promise.all([
        fetch(`/api/reports/dashboard?${storeParam}`),
        fetch(`/api/reports/top-products?limit=5&days=7&${storeParam}`),
        fetch(`/api/reports/inventory-status?status=low&limit=5&${storeParam}`),
        fetch(`/api/sales?limit=5&${storeParam}`),
      ]);

      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json();
        setDashboardStats(dashboardData);
      }

      if (topProductsResponse.ok) {
        const topProductsData = await topProductsResponse.json();
        setTopProducts(topProductsData.products || []);
      }

      if (inventoryResponse.ok) {
        const inventoryData = await inventoryResponse.json();
        setLowStockProducts(inventoryData.products || []);
      }

      if (salesResponse.ok) {
        const salesData = await salesResponse.json();
        setRecentSales(salesData.sales || []);
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-GH", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Dashboard Overview
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Welcome to your Shopper POS dashboard. Here's an overview of your business.
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="secondary"
              onClick={fetchDashboardData}
              disabled={isLoading || !selectedStoreId}
            >
              {isLoading ? "Refreshing..." : "Refresh Data"}
            </Button>
            <Link
              href="/stores"
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"
            >
              Manage Stores
            </Link>
          </div>
        </div>

        {/* Store Selector */}
        <div className="max-w-md">
          <div className="space-y-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Select Store
            </label>
            <select
              value={selectedStoreId || ""}
              onChange={(e) => setSelectedStoreId(parseInt(e.target.value))}
              disabled={storesLoading}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50"
            >
              <option value="">
                {storesLoading ? "Loading stores..." : "Select a store..."}
              </option>
              {stores.map((store) => (
                <option key={store.id} value={store.id}>
                  {store.name} - {store.location}
                </option>
              ))}
            </select>
          </div>
          {selectedStore && (
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {selectedStore.address}
            </p>
          )}
        </div>
      </div>

      {/* Quick Stats Grid */}
      {isLoading ? (
        <FormGrid cols={4} gap="md">
          {[1, 2, 3, 4].map((i) => (
            <FormCard key={i} title="Loading...">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-400 dark:text-gray-600">
                  <div className="animate-pulse">---</div>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Loading...</p>
              </div>
            </FormCard>
          ))}
        </FormGrid>
      ) : (
        <FormGrid cols={4} gap="md">
          <FormCard title="Today's Sales">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                <AnimatedCounter amount={dashboardStats?.sales.today.total || 0} />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {dashboardStats?.sales.today.count || 0} transactions
              </p>
              {dashboardStats?.sales.today.growth !== undefined && (
                <p className={`text-xs mt-1 ${dashboardStats.sales.today.growth >= 0 ? "text-green-600" : "text-red-600"}`}>
                  {dashboardStats.sales.today.growth >= 0 ? "+" : ""}{dashboardStats.sales.today.growth.toFixed(1)}% from yesterday
                </p>
              )}
            </div>
          </FormCard>

          <FormCard title="This Week">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                <AnimatedCounter amount={dashboardStats?.sales.week.total || 0} />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {dashboardStats?.sales.week.count || 0} transactions
              </p>
              {dashboardStats?.sales.week.growth !== undefined && (
                <p className={`text-xs mt-1 ${dashboardStats.sales.week.growth >= 0 ? "text-green-600" : "text-red-600"}`}>
                  {dashboardStats.sales.week.growth >= 0 ? "+" : ""}{dashboardStats.sales.week.growth.toFixed(1)}% from last week
                </p>
              )}
            </div>
          </FormCard>

          <FormCard title="Total Products">
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                {dashboardStats?.inventory.totalProducts || 0}
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Active products
              </p>
              {dashboardStats?.inventory.lowStockCount !== undefined && (
                <p className={`text-xs mt-1 ${dashboardStats.inventory.lowStockCount > 0 ? "text-red-600" : "text-green-600"}`}>
                  {dashboardStats.inventory.lowStockCount} low stock items
                </p>
              )}
            </div>
          </FormCard>

          <FormCard title="Total Customers">
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">
                {dashboardStats?.customers.total || 0}
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Registered customers
              </p>
            </div>
          </FormCard>
        </FormGrid>
      )}

      {/* Main Content Grid */}
      <FormGrid cols={2} gap="lg">
        {/* Top Products This Week */}
        <FormCard title="Top Products This Week" description="Best performing products by revenue">
          {isLoading ? (
            <div className="animate-pulse space-y-3">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex justify-between items-center">
                  <div className="h-4 bg-gray-200 rounded w-1/2 dark:bg-gray-700"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/4 dark:bg-gray-700"></div>
                </div>
              ))}
            </div>
          ) : topProducts.length > 0 ? (
            <div className="space-y-3">
              {topProducts.map((product, index) => (
                <div key={product.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium dark:bg-blue-800 dark:text-blue-100">
                        {index + 1}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">{product.name}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{product.category} • {product.units} units</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatAmount(product.revenue)}
                    </p>
                    <p className={`text-xs ${product.growth >= 0 ? "text-green-600" : "text-red-600"}`}>
                      {product.growth >= 0 ? "+" : ""}{product.growth.toFixed(1)}%
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <p className="text-gray-500 dark:text-gray-400">No sales data available</p>
            </div>
          )}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Link
              href="/reports"
              className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
            >
              View detailed reports →
            </Link>
          </div>
        </FormCard>

        {/* Low Stock Alerts */}
        <FormCard title="Low Stock Alerts" description="Products that need restocking">
          {isLoading ? (
            <div className="animate-pulse space-y-3">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex justify-between items-center">
                  <div className="h-4 bg-gray-200 rounded w-1/2 dark:bg-gray-700"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/4 dark:bg-gray-700"></div>
                </div>
              ))}
            </div>
          ) : lowStockProducts.length > 0 ? (
            <div className="space-y-3">
              {lowStockProducts.map((product) => (
                <div key={product.id} className="flex justify-between items-center p-3 bg-red-50 rounded-lg dark:bg-red-900/20">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{product.name}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{product.category}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-red-600 dark:text-red-400">
                      {product.currentStock} / {product.minStock}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Current / Min</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <div className="text-green-600 dark:text-green-400 mb-2">
                <svg className="mx-auto h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <p className="text-gray-500 dark:text-gray-400">All products are well stocked!</p>
            </div>
          )}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Link
              href="/inventory"
              className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Manage inventory →
            </Link>
          </div>
        </FormCard>
      </FormGrid>

      {/* Recent Sales Activity */}
      <FormCard title="Recent Sales Activity" description="Latest transactions and customer activity">
        {isLoading ? (
          <div className="animate-pulse space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex justify-between items-center py-3">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 bg-gray-200 rounded-full dark:bg-gray-700"></div>
                  <div className="space-y-1">
                    <div className="h-4 bg-gray-200 rounded w-32 dark:bg-gray-700"></div>
                    <div className="h-3 bg-gray-200 rounded w-24 dark:bg-gray-700"></div>
                  </div>
                </div>
                <div className="h-4 bg-gray-200 rounded w-20 dark:bg-gray-700"></div>
              </div>
            ))}
          </div>
        ) : recentSales.length > 0 ? (
          <div className="flow-root">
            <ul className="-mb-8">
              {recentSales.map((sale, index) => (
                <li key={sale.id}>
                  <div className="relative pb-8">
                    {index !== recentSales.length - 1 && (
                      <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700" aria-hidden="true" />
                    )}
                    <div className="relative flex space-x-3">
                      <div>
                        <span className="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white dark:ring-gray-800">
                          <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Sale completed by <span className="font-medium text-gray-900 dark:text-white">{sale.user.name}</span>
                            {sale.customer && (
                              <span> for <span className="font-medium text-gray-900 dark:text-white">{sale.customer.name}</span></span>
                            )}
                          </p>
                          <p className="text-xs text-gray-400 dark:text-gray-500">
                            {sale.itemCount} item{sale.itemCount !== 1 ? 's' : ''} • {formatAmount(sale.total)}
                          </p>
                        </div>
                        <div className="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                          {formatDate(sale.createdAt)}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="text-center py-6">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <p className="mt-2 text-gray-500 dark:text-gray-400">No recent sales activity</p>
          </div>
        )}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Link
            href="/sales"
            className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
          >
            View all sales →
          </Link>
        </div>
      </FormCard>

      {/* Quick Actions */}
      <FormCard title="Quick Actions" description="Common tasks and shortcuts">
        <FormGrid cols={4} gap="md">
          <Link
            href="/sales/new"
            className="group relative bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white hover:from-blue-600 hover:to-blue-700 transition-all duration-200"
          >
            <div className="flex items-center justify-center mb-3">
              <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-center">New Sale</h3>
            <p className="text-sm text-blue-100 text-center mt-1">Start a new transaction</p>
          </Link>

          <Link
            href="/products/new"
            className="group relative bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white hover:from-green-600 hover:to-green-700 transition-all duration-200"
          >
            <div className="flex items-center justify-center mb-3">
              <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-center">Add Product</h3>
            <p className="text-sm text-green-100 text-center mt-1">Add new inventory item</p>
          </Link>

          <Link
            href="/inventory/adjustments"
            className="group relative bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-lg text-white hover:from-purple-600 hover:to-purple-700 transition-all duration-200"
          >
            <div className="flex items-center justify-center mb-3">
              <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-center">Stock Adjustment</h3>
            <p className="text-sm text-purple-100 text-center mt-1">Update inventory levels</p>
          </Link>

          <Link
            href="/reports"
            className="group relative bg-gradient-to-r from-yellow-500 to-yellow-600 p-6 rounded-lg text-white hover:from-yellow-600 hover:to-yellow-700 transition-all duration-200"
          >
            <div className="flex items-center justify-center mb-3">
              <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-center">View Reports</h3>
            <p className="text-sm text-yellow-100 text-center mt-1">Business analytics</p>
          </Link>
        </FormGrid>
      </FormCard>
    </div>
  );
}
