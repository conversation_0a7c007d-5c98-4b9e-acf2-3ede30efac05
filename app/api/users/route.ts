import { db, prisma } from "@/lib/db";
import { CreateUserSchema } from "@/lib/dto";
import { validateRequestBody } from "@/lib/validation";
import { NextRequest, NextResponse } from "next/server";

export async function GET() {
  try {
    const users = await db.users.getAll();
    return NextResponse.json(users);
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Validate request body using DTO schema
    const validatedData = await validateRequestBody(request, CreateUserSchema);

    // Hash the password
    const bcrypt = require("bcryptjs");
    const hashedPassword = await bcrypt.hash(validatedData.password, 12);

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "A user with this email already exists" },
        { status: 409 },
      );
    }

    // Create user with hashed password
    const { password, ...userData } = validatedData;
    const user = await prisma.user.create({
      data: {
        ...userData,
        password: hashedPassword,
      },
      include: {
        store: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
    });

    // Remove password from response
    const { password: _, ...userResponse } = user;
    return NextResponse.json(userResponse, { status: 201 });
  } catch (error: any) {
    console.error("Error creating user:", error);

    // Handle validation errors
    if (error.statusCode === 400) {
      return NextResponse.json(
        {
          error: "Validation failed",
          errors: error.errors,
          fieldErrors: error.fieldErrors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 },
    );
  }
}
