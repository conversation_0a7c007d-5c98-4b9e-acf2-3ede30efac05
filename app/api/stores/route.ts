import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeStats = searchParams.get("includeStats") === "true";

    if (includeStats) {
      // Fetch stores with statistics
      const stores = await prisma.store.findMany({
        include: {
          _count: {
            select: {
              users: true,
              products: true,
              sales: true,
            },
          },
          sales: {
            where: {
              status: "COMPLETED",
              createdAt: {
                gte: new Date(new Date().setDate(new Date().getDate() - 30)), // Last 30 days
              },
            },
            select: {
              total: true,
            },
          },
        },
        orderBy: { name: "asc" },
      });

      // Calculate revenue for each store
      const storesWithStats = stores.map(store => ({
        id: store.id,
        name: store.name,
        address: store.address,
        location: store.location,
        phoneNumber: store.phoneNumber,
        email: store.email,
        website: store.website,
        logoUrl: store.logoUrl,
        ceo: store.ceo,
        ceoContact: store.ceoContact,
        ceoEmail: store.ceoEmail,
        facebook: store.facebook,
        whatsapp: store.whatsapp,
        telegram: store.telegram,
        visionStatement: store.visionStatement,
        missionStatement: store.missionStatement,
        mainGoal: store.mainGoal,
        mainObjective: store.mainObjective,
        createdAt: store.createdAt,
        updatedAt: store.updatedAt,
        stats: {
          totalUsers: store._count.users,
          totalProducts: store._count.products,
          totalSales: store._count.sales,
          monthlyRevenue: store.sales.reduce((sum, sale) => sum + sale.total, 0),
        },
      }));

      return NextResponse.json({ stores: storesWithStats });
    } else {
      // Fetch basic store information
      const stores = await prisma.store.findMany({
        select: {
          id: true,
          name: true,
          address: true,
          location: true,
          phoneNumber: true,
          email: true,
          logoUrl: true,
        },
        orderBy: { name: "asc" },
      });

      return NextResponse.json({ stores });
    }
  } catch (error) {
    console.error("Error fetching stores:", error);
    return NextResponse.json(
      { error: "Failed to fetch stores" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      address,
      location,
      phoneNumber,
      email,
      website,
      logoUrl,
      ceo,
      ceoContact,
      ceoEmail,
      facebook,
      whatsapp,
      telegram,
      visionStatement,
      missionStatement,
      mainGoal,
      mainObjective,
    } = body;

    // Validate required fields
    if (!name || !address || !location || !phoneNumber || !email || !ceo || !ceoContact || !ceoEmail) {
      return NextResponse.json(
        { error: "Missing required fields: name, address, location, phoneNumber, email, ceo, ceoContact, and ceoEmail are required" },
        { status: 400 }
      );
    }

    // Check if store name already exists
    const existingStore = await prisma.store.findUnique({
      where: { name },
    });

    if (existingStore) {
      return NextResponse.json(
        { error: "A store with this name already exists" },
        { status: 409 }
      );
    }

    // Create the new store
    const newStore = await prisma.store.create({
      data: {
        name,
        address,
        location,
        phoneNumber,
        email,
        website: website || null,
        logoUrl: logoUrl || null,
        ceo,
        ceoContact,
        ceoEmail,
        facebook: facebook || null,
        whatsapp: whatsapp || null,
        telegram: telegram || null,
        visionStatement: visionStatement || null,
        missionStatement: missionStatement || null,
        mainGoal: mainGoal || null,
        mainObjective: mainObjective || null,
      },
    });

    return NextResponse.json({
      store: newStore,
      message: "Store created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating store:", error);
    return NextResponse.json(
      { error: "Failed to create store" },
      { status: 500 }
    );
  }
}
