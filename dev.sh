#!/usr/bin/env bash
# dev.sh – start Postgres (if needed) and then launch Next.js

set -euo pipefail

# ──────────────── Configuration ────────────────
COMPOSE_FILE="postgres-podman/podman-compose.yml"   # compose file to use
SERVICE_NAME="postgres_db"          # container name from compose
DEV_CMD="pnpm dev"                  # change to `npm run dev` or `yarn dev` if you prefer
PG_USER="shopper_user"              # must match POSTGRES_USER in compose
# ───────────────────────────────────────────────

is_running() {
  podman ps --format '{{.Names}}' | grep -q "^${SERVICE_NAME}$"
}

start_db() {
  echo "🔄  Starting PostgreSQL container with <PERSON><PERSON> Compose…"
  podman-compose -f "$COMPOSE_FILE" up -d
}

wait_for_db() {
  echo "⏳  Waiting for PostgreSQL to be ready…"
  until podman exec "$SERVICE_NAME" pg_isready -U "$PG_USER" > /dev/null 2>&1; do
    sleep 1
  done
  echo "✅  PostgreSQL is ready!"
}

# ───────────── Main flow ─────────────
if is_running; then
  echo "✅  ${SERVICE_NAME} already running; skipping start."
else
  start_db
  wait_for_db
fi

echo "🚀  Launching Next.js dev server (${DEV_CMD})…"
exec $DEV_CMD
