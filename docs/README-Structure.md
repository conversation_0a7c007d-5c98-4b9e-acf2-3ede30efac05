# Shopper POS - Next.js App Router Structure

## 📁 Project Structure

```
app/
│
├── layout.tsx            # Root layout (global <html>, <body>)
├── globals.css           # Tailwind and Flowbite styles
├── page.tsx              # Landing page with hero section
│
├── dashboard/            # Protected admin dashboard
│   ├── layout.tsx        # Dashboard layout with sidebar navigation
│   ├── page.tsx          # Dashboard overview with stats
│   ├── products/         # Product management
│   │   ├── page.tsx      # Product list with search/filters
│   │   └── [id]/         # Dynamic product routes
│   │       └── page.tsx  # Product edit/details page
│   ├── sales/            # Sales and transactions
│   │   └── page.tsx      # Sales list and analytics
│   ├── inventory/        # Inventory management
│   │   └── page.tsx      # Stock levels and movements
│   ├── reports/          # Reports and analytics
│   │   └── page.tsx      # Business insights and reports
│   └── settings/         # Business settings
│       └── page.tsx      # POS configuration
│
├── login/                # Authentication
│   └── page.tsx          # Login form
└── register/             # User registration
    └── page.tsx          # Registration form

components/
├── Navs.tsx              # Reusable navigation components
├── examples/             # Usage examples
└── README-Nav.md         # Navigation documentation

types/
└── index.d.ts            # TypeScript definitions
```

## 🚀 Key Features

### ✅ **Modern Next.js App Router**
- File-based routing with layouts
- Nested layouts for dashboard
- Dynamic routes for product details
- Proper metadata for SEO

### ✅ **Reusable Navigation System**
- `AdminLayout` - Complete dashboard layout
- `SidebarNav` - Collapsible sidebar navigation
- `TopNavbar` - Top navigation bar
- Mobile responsive with hamburger menu

### ✅ **TypeScript Integration**
- Full type safety throughout
- Proper interface definitions
- IntelliSense support

### ✅ **Responsive Design**
- Mobile-first approach
- Dark mode support
- Tailwind CSS styling
- Flowbite components

## 📖 Usage Examples

### Basic Dashboard Page
```tsx
// app/dashboard/my-page/page.tsx
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "My Page - Shopper POS",
  description: "Description of my page",
};

export default function MyPage() {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">My Page</h1>
      {/* Your content here */}
    </div>
  );
}
```

### Custom Navigation Links
```tsx
// In dashboard/layout.tsx
const customLinks: NavLink[] = [
  {
    name: "Custom Page",
    href: "/dashboard/custom",
    icon: <YourIcon className="h-5 w-5" />,
  },
];

<AdminLayout sidebarLinks={customLinks}>
  {children}
</AdminLayout>
```

## 🛠 Development Workflow

### Adding New Dashboard Pages
1. Create new folder in `app/dashboard/`
2. Add `page.tsx` with proper metadata
3. Content automatically gets dashboard layout
4. Add navigation link to `dashboard/layout.tsx`

### Adding New Routes
1. Create folder structure in `app/`
2. Add `page.tsx` for the route
3. Optional: Add `layout.tsx` for nested layouts

### Customizing Navigation
1. Edit `dashboardLinks` in `app/dashboard/layout.tsx`
2. Add new icons and routes as needed
3. Navigation automatically updates

## 🎯 Route Overview

| Route | Purpose | Layout |
|-------|---------|--------|
| `/` | Landing page | Root layout |
| `/login` | User authentication | Root layout |
| `/register` | User registration | Root layout |
| `/dashboard` | Dashboard overview | Dashboard layout |
| `/dashboard/products` | Product management | Dashboard layout |
| `/dashboard/products/[id]` | Product details | Dashboard layout |
| `/dashboard/sales` | Sales tracking | Dashboard layout |
| `/dashboard/inventory` | Inventory management | Dashboard layout |
| `/dashboard/reports` | Business analytics | Dashboard layout |
| `/dashboard/settings` | System configuration | Dashboard layout |

## 🔧 Configuration

### Navigation Links
Edit `app/dashboard/layout.tsx` to customize sidebar navigation:

```tsx
const dashboardLinks: NavLink[] = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: <DashboardIcon />,
  },
  // Add more links...
];
```

### Metadata
Each page includes proper metadata for SEO:

```tsx
export const metadata: Metadata = {
  title: "Page Title - Shopper POS",
  description: "Page description for SEO",
};
```

### Styling
- Global styles in `app/globals.css`
- Tailwind CSS for utility classes
- Flowbite components for UI elements
- Dark mode support throughout

## 🚀 Next Steps

1. **Add Authentication**: Implement proper auth with middleware
2. **Database Integration**: Connect to your preferred database
3. **API Routes**: Add API endpoints for data operations
4. **Testing**: Add unit and integration tests
5. **Deployment**: Deploy to Vercel or your preferred platform

## 📚 Documentation

- [Navigation Components](./components/README-Nav.md)
- [TypeScript Types](./types/index.d.ts)
- [Next.js App Router](https://nextjs.org/docs/app)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Flowbite React](https://flowbite-react.com/)

## 🎉 Ready to Use!

Your Shopper POS application structure is now complete and ready for development. The navigation system is fully functional, responsive, and customizable. Start by running:

```bash
npm run dev
```

Then visit:
- `http://localhost:3000` - Landing page
- `http://localhost:3000/dashboard` - Dashboard
- `http://localhost:3000/login` - Login page
