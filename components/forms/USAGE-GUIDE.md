# Form Components Usage Guide

## 🚀 Quick Start

Import the components you need:

```tsx
import {
  FormCard,
  FormGrid,
  FormActions,
  Input,
  EmailInput,
  PasswordInput,
  Select,
  Textarea,
  Checkbox,
  CurrencyInput,
  SearchInput,
  SubmitButton,
  CancelButton,
} from "@/components/forms";
```

## 📋 Basic Form Example

```tsx
"use client";

import { useState } from "react";
import {
  FormCard,
  FormGrid,
  FormActions,
  Input,
  EmailInput,
  Select,
  SubmitButton,
  CancelButton,
} from "@/components/forms";

export function ContactForm() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const subjectOptions = [
    { value: "general", label: "General Inquiry" },
    { value: "support", label: "Technical Support" },
    { value: "billing", label: "Billing Question" },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Your form submission logic here
      await submitForm(formData);
    } catch (error) {
      setErrors({ general: "Something went wrong" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <FormCard title="Contact Us" description="Send us a message">
      <form onSubmit={handleSubmit} className="space-y-6">
        <FormGrid cols={2} gap="md">
          <Input
            name="name"
            label="Full Name"
            placeholder="Enter your name"
            required
            value={formData.name}
            onChange={(e) => handleChange("name", e.target.value)}
            error={errors.name}
          />

          <EmailInput
            name="email"
            label="Email Address"
            placeholder="Enter your email"
            required
            value={formData.email}
            onChange={(e) => handleChange("email", e.target.value)}
            error={errors.email}
          />
        </FormGrid>

        <Select
          name="subject"
          label="Subject"
          options={subjectOptions}
          placeholder="Select a subject"
          required
          value={formData.subject}
          onChange={(e) => handleChange("subject", e.target.value)}
          error={errors.subject}
        />

        <Textarea
          name="message"
          label="Message"
          placeholder="Enter your message"
          rows={4}
          required
          value={formData.message}
          onChange={(e) => handleChange("message", e.target.value)}
          error={errors.message}
        />

        <FormActions align="right">
          <CancelButton type="button">Cancel</CancelButton>
          <SubmitButton loading={isSubmitting}>
            {isSubmitting ? "Sending..." : "Send Message"}
          </SubmitButton>
        </FormActions>
      </form>
    </FormCard>
  );
}
```

## 🛍️ E-commerce Product Form

```tsx
import {
  FormCard,
  FormSection,
  FormGrid,
  Input,
  Textarea,
  CategorySelect,
  CurrencyInput,
  NumberInput,
  Checkbox,
  SubmitButton,
} from "@/components/forms";

export function ProductForm() {
  return (
    <form className="space-y-8">
      <FormSection
        title="Product Information"
        description="Basic product details"
      >
        <FormGrid cols={2} gap="md">
          <Input
            name="name"
            label="Product Name"
            placeholder="Enter product name"
            required
          />

          <Input name="sku" label="SKU" placeholder="Product SKU" required />

          <CategorySelect name="category" label="Category" required />

          <Input name="brand" label="Brand" placeholder="Product brand" />
        </FormGrid>

        <Textarea
          name="description"
          label="Description"
          placeholder="Product description"
          rows={3}
        />
      </FormSection>

      <FormSection
        title="Pricing & Inventory"
        description="Set pricing and stock information"
      >
        <FormGrid cols={3} gap="md">
          <CurrencyInput name="price" label="Price" required minValue={0} />

          <CurrencyInput
            name="costPrice"
            label="Cost Price"
            required
            minValue={0}
          />

          <NumberInput name="stock" label="Stock Quantity" min={0} required />
        </FormGrid>

        <Checkbox
          name="isActive"
          label="Active Product"
          helpText="Product is available for sale"
        />
      </FormSection>

      <FormActions>
        <SubmitButton>Save Product</SubmitButton>
      </FormActions>
    </form>
  );
}
```

## 🔍 Search Form

```tsx
import { SearchInput, Select, Button } from "@/components/forms";

export function ProductSearch() {
  const [filters, setFilters] = useState({
    query: "",
    category: "",
    status: "",
  });

  const categoryOptions = [
    { value: "", label: "All Categories" },
    { value: "electronics", label: "Electronics" },
    { value: "clothing", label: "Clothing" },
  ];

  const statusOptions = [
    { value: "", label: "All Status" },
    { value: "active", label: "Active" },
    { value: "inactive", label: "Inactive" },
  ];

  return (
    <div className="rounded-lg bg-white p-6 shadow">
      <FormGrid cols={4} gap="md">
        <SearchInput
          name="search"
          placeholder="Search products..."
          value={filters.query}
          onSearch={(query) => setFilters((prev) => ({ ...prev, query }))}
          debounceMs={300}
        />

        <Select
          name="category"
          placeholder="Category"
          options={categoryOptions}
          value={filters.category}
          onChange={(e) =>
            setFilters((prev) => ({ ...prev, category: e.target.value }))
          }
        />

        <Select
          name="status"
          placeholder="Status"
          options={statusOptions}
          value={filters.status}
          onChange={(e) =>
            setFilters((prev) => ({ ...prev, status: e.target.value }))
          }
        />

        <Button variant="primary" className="w-full">
          Search
        </Button>
      </FormGrid>
    </div>
  );
}
```

## 💰 Financial Form

```tsx
import { CurrencyInput, NumberInput, Select } from "@/components/forms";

export function InvoiceForm() {
  return (
    <FormCard title="Create Invoice">
      <FormGrid cols={2} gap="md">
        <CurrencyInput
          name="amount"
          label="Amount"
          currency="GHS"
          required
          minValue={0}
        />

        <NumberInput name="quantity" label="Quantity" min={1} required />

        <Select
          name="taxRate"
          label="Tax Rate"
          options={[
            { value: "0", label: "0%" },
            { value: "8.25", label: "8.25%" },
            { value: "10", label: "10%" },
          ]}
        />

        <CurrencyInput name="discount" label="Discount" minValue={0} />
      </FormGrid>
    </FormCard>
  );
}
```

## 🎨 Styling Tips

### Custom Styling

```tsx
<Input
  name="custom"
  label="Custom Input"
  className="border-2 border-purple-300 focus:border-purple-500"
/>
```

### Error States

```tsx
<Input
  name="email"
  label="Email"
  error="Please enter a valid email address"
  value={email}
  onChange={handleEmailChange}
/>
```

### Help Text

```tsx
<Input
  name="password"
  label="Password"
  type="password"
  helpText="Must be at least 8 characters long"
/>
```

### Loading States

```tsx
<SubmitButton loading={isSubmitting} disabled={isSubmitting}>
  {isSubmitting ? "Saving..." : "Save"}
</SubmitButton>
```

## 🔧 Advanced Usage

### With React Hook Form

```tsx
import { useForm } from "react-hook-form";
import { Input, SubmitButton } from "@/components/forms";

export function HookFormExample() {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Input
        {...register("email", { required: "Email is required" })}
        label="Email"
        error={errors.email?.message}
      />

      <SubmitButton>Submit</SubmitButton>
    </form>
  );
}
```

### With Validation

```tsx
import { z } from "zod";

const schema = z.object({
  email: z.string().email("Invalid email"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

// Use with your preferred validation library
```

## 📱 Responsive Design

All components are mobile-first and responsive:

```tsx
<FormGrid cols={1} gap="md" className="sm:grid-cols-2 lg:grid-cols-3">
  {/* Responsive grid: 1 col on mobile, 2 on tablet, 3 on desktop */}
</FormGrid>
```

## 🌙 Dark Mode

All components automatically support dark mode through Tailwind's `dark:` classes. No additional configuration needed!
